"""
API Views for frontend integration
Provides endpoints for debug mode and production authentication
"""

import time
import logging
from django.http import JsonResponse
from django.utils import timezone
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.conf import settings

logger = logging.getLogger(__name__)
import json
import logging

from apps.user.models import UserProfile
from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class DebugUsersView(View):
    """
    Debug API endpoint to list all users for frontend debug panel
    Only available in debug mode
    """

    def get(self, request):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)

        try:
            users = []
            for profile in UserProfile.objects.select_related('user').all()[:50]:  # Limit to 50 users
                users.append({
                    'id': str(profile.id),
                    'name': profile.profile_name or f"User {profile.id}",
                    'is_real': getattr(profile, 'is_real', False),
                })

            return JsonResponse(users, safe=False)
        except Exception as e:
            logger.error(f"Error fetching users for debug: {e}")
            return JsonResponse({'error': 'Failed to fetch users'}, status=500)

    def post(self, request):
        """Create a new test user profile"""
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)
 
        try:
            import json
            import uuid
            from apps.user.models import Demographics

            # Parse request data
            data = json.loads(request.body) if request.body else {}
            profile_type = data.get('profile_type', 'german_student')

            # Generate unique username
            timestamp = str(int(time.time()))
            username = f"debug_user_{timestamp}"

            # Create Django auth user
            from django.contrib.auth import get_user_model
            User = get_user_model()
            auth_user = User.objects.create_user(
                username=username,
                email=f"{username}@debug.local",
                first_name="Debug",
                last_name="User"
            )

            # Create UserProfile
            if profile_type == 'german_student':
                profile_name = f"Emma Schmidt (Debug {timestamp[-4:]})"
                user_profile = UserProfile.objects.create(
                    user=auth_user,
                    profile_name=profile_name,
                    is_real=False  # This is a debug profile
                )

                # Create demographics for German 22-year-old female student
                Demographics.objects.create(
                    user_profile=user_profile,
                    full_name=profile_name,
                    age=22,
                    gender="Female",
                    location="Berlin, Germany",
                    language="German, English",
                    occupation="Computer Science Student"
                )
            else:
                # Default profile
                profile_name = f"Debug User {timestamp[-4:]}"
                user_profile = UserProfile.objects.create(
                    user=auth_user,
                    profile_name=profile_name,
                    is_real=False
                )

            logger.info(f"Created debug user profile: {profile_name} (ID: {user_profile.id})")

            return JsonResponse({
                'success': True,
                'user': {
                    'id': str(user_profile.id),
                    'name': user_profile.profile_name,
                    'is_real': False,
                },
                'message': f'Created new debug user: {profile_name}'
            })

        except Exception as e:
            logger.error(f"Error creating debug user: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return JsonResponse({'error': f'Failed to create user: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class DebugUserDetailView(View):
    """
    Debug API endpoint to get detailed user information
    Only available in debug mode
    """

    def get(self, request, user_id):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)

        try:
            from apps.user.models import Demographics, Preference

            # Get user profile
            user_profile = UserProfile.objects.select_related('user').get(id=user_id)

            # Get demographics
            demographics = None
            try:
                demo = Demographics.objects.get(user_profile=user_profile)
                demographics = {
                    'full_name': demo.full_name,
                    'age': demo.age,
                    'gender': demo.gender,
                    'location': demo.location,
                    'language': demo.language,
                    'occupation': demo.occupation,
                }
            except Demographics.DoesNotExist:
                pass

            # Get preferences
            preferences = []
            for pref in Preference.objects.filter(user_profile=user_profile):
                preferences.append({
                    'name': pref.pref_name,
                    'description': pref.pref_description,
                    'strength': pref.pref_strength,
                })

            # Calculate profile completion percentage
            profile_completion = 0
            if demographics:
                profile_completion += 25  # Demographics
            if preferences:
                profile_completion += min(75, len(preferences) * 15)  # Preferences (up to 75%)

            user_details = {
                'id': str(user_profile.id),
                'profile_name': user_profile.profile_name,
                'is_real': getattr(user_profile, 'is_real', False),
                'profile_completion_percentage': profile_completion,
                'demographics': demographics,
                'preferences': preferences,
                'created_at': user_profile.created_at.isoformat() if hasattr(user_profile, 'created_at') else None,
            }

            return JsonResponse(user_details)

        except UserProfile.DoesNotExist:
            return JsonResponse({'error': 'User not found'}, status=404)
        except Exception as e:
            logger.error(f"Error fetching user details for {user_id}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return JsonResponse({'error': f'Failed to fetch user details: {str(e)}'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class DebugLLMConfigsView(View):
    """
    Debug API endpoint to list all LLM configurations for frontend debug panel
    Only available in debug mode
    """
    
    def get(self, request):
        if not settings.DEBUG:
            return JsonResponse({'error': 'Debug endpoints not available in production'}, status=403)
        
        try:
            configs = []
            for config in LLMConfig.objects.all():
                configs.append({
                    'id': str(config.id),
                    'name': config.name,
                    'model_name': config.model_name,
                    'temperature': float(config.temperature),
                    'is_default': config.is_default,
                })
            
            return JsonResponse(configs, safe=False)
        except Exception as e:
            logger.error(f"Error fetching LLM configs for debug: {e}")
            return JsonResponse({'error': 'Failed to fetch LLM configs'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AuthLoginView(View):
    """
    Authentication endpoint for production mode
    """
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return JsonResponse({'error': 'Username and password required'}, status=400)
            
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                
                # Get user profile
                try:
                    profile = UserProfile.objects.get(user=user)
                    user_data = {
                        'id': str(profile.id),
                        'name': profile.profile_name or user.username,
                        'username': user.username,
                        'email': user.email,
                        'is_staff': user.is_staff,
                    }
                except UserProfile.DoesNotExist:
                    user_data = {
                        'id': str(user.id),
                        'name': user.username,
                        'username': user.username,
                        'email': user.email,
                        'is_staff': user.is_staff,
                    }
                
                return JsonResponse({
                    'success': True,
                    'token': 'session-based',  # Using Django sessions
                    'expires_in': 3600,  # 1 hour
                    'user': user_data,
                    'permissions': ['user']
                })
            else:
                return JsonResponse({'error': 'Invalid credentials'}, status=401)
                
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Login error: {e}")
            return JsonResponse({'error': 'Login failed'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AuthVerifyView(View):
    """
    Token verification endpoint for production mode
    """
    
    def post(self, request):
        if request.user.is_authenticated:
            try:
                profile = UserProfile.objects.get(user=request.user)
                user_data = {
                    'id': str(profile.id),
                    'name': profile.profile_name or request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }
            except UserProfile.DoesNotExist:
                user_data = {
                    'id': str(request.user.id),
                    'name': request.user.username,
                    'username': request.user.username,
                    'email': request.user.email,
                    'is_staff': request.user.is_staff,
                }
            
            return JsonResponse({
                'valid': True,
                'expires_in': 3600,
                'user': user_data,
                'permissions': ['user']
            })
        else:
            return JsonResponse({'valid': False}, status=401)


@method_decorator(csrf_exempt, name='dispatch')
class AuthLogoutView(View):
    """
    Logout endpoint for production mode
    """
    
    def post(self, request):
        from django.contrib.auth import logout
        logout(request)
        return JsonResponse({'success': True})


@method_decorator(csrf_exempt, name='dispatch')
class ActivityCatalogView(View):
    """
    API endpoint to load activity catalog (generic and tailored activities)
    """

    def get(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from django.db.models import Q

            # Get search parameters
            search_query = request.GET.get('search', '').strip()
            limit = int(request.GET.get('limit', 30))
            offset = int(request.GET.get('offset', 0))

            # Get user profile if authenticated
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    pass

            # Build search filter
            search_filter = Q()
            if search_query:
                search_filter = (
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query) |
                    Q(instructions__icontains=search_query)
                )

            # Load generic activities with search
            generic_activities = []
            generic_queryset = GenericActivity.objects.filter(search_filter) if search_query else GenericActivity.objects.all()
            for activity in generic_queryset[offset:offset+limit//2]:
                generic_activities.append({
                    'id': f'generic-{activity.id}',
                    'name': activity.name,
                    'description': activity.description,
                    'domain': 'general',  # Default domain
                    'base_challenge_rating': 50,  # Default challenge rating for generic activities
                    'type': 'generic',
                    'icon': '🎯'
                })

            # Load tailored activities for the user with search
            tailored_activities = []
            if user_profile:
                tailored_queryset = ActivityTailored.objects.for_user(user_profile).filter(search_filter) if search_query else ActivityTailored.objects.for_user(user_profile)
                for activity in tailored_queryset[offset:offset+limit//2]:
                    tailored_activities.append({
                        'id': f'tailored-{activity.id}',
                        'name': activity.name,
                        'description': activity.description,
                        'domain': 'personalized',
                        'base_challenge_rating': activity.base_challenge_rating,
                        'type': 'tailored',
                        'icon': '⭐' if activity.created_by is None else '👤'  # Different icon for user-created
                    })

            # Combine activities (tailored first, then generic)
            all_activities = tailored_activities + generic_activities

            return JsonResponse({
                'success': True,
                'activities': all_activities,
                'total_count': len(all_activities),
                'tailored_count': len(tailored_activities),
                'generic_count': len(generic_activities),
                'search_query': search_query,
                'has_more': len(all_activities) == limit  # Indicate if there are more results
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'activities': []
            }, status=500)


# IngressDispatcherView removed - using standard Django URL routing instead


@method_decorator(csrf_exempt, name='dispatch')
class HealthCheckView(View):
    """
    Health check endpoint for frontend to verify backend availability
    """

    def get(self, request):
        try:
            # Debug: Log request headers to understand ingress routing
            logger.info(f"Health check request headers: {dict(request.headers)}")
            logger.info(f"Health check request path: {request.path}")
            logger.info(f"Health check request full path: {request.get_full_path()}")

            # Basic health check - verify database connection
            from django.db import connection
            from datetime import datetime

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")

            return JsonResponse({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'connected',
                'services': {
                    'websocket': 'available',
                    'workflows': 'available',
                    'llm': 'available'
                }
            })
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JsonResponse({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class LoggingView(View):
    """
    Error logging endpoint for production mode
    """

    def post(self, request):
        try:
            data = json.loads(request.body)
            level = data.get('level', 'error')
            message = data.get('message', '')
            details = data.get('details', {})

            # Log the frontend error
            if level == 'error':
                logger.error(f"Frontend Error: {message}", extra={'details': details})
            elif level == 'warning':
                logger.warning(f"Frontend Warning: {message}", extra={'details': details})
            else:
                logger.info(f"Frontend Info: {message}", extra={'details': details})

            return JsonResponse({'success': True})

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Logging error: {e}")
            return JsonResponse({'error': 'Logging failed'}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class CreateActivityView(View):
    """
    API endpoint to create a new custom activity
    """

    def post(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from datetime import datetime
            import json

            # Parse request data
            data = json.loads(request.body)

            # Get user profile
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)
            else:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Create new generic activity first (as template)
            generic_activity = GenericActivity.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                code=f"custom_{user_profile.id}_{int(time.time())}",  # Unique code
                duration_range=data.get('duration_range', '15-30 minutes'),
                instructions=data.get('instructions', data.get('description', '')),
                created_on=datetime.now().date(),
                social_requirements={}
            )

            # Get user's current environment
            current_environment = user_profile.current_environment
            if not current_environment:
                # Get first available environment for the user
                current_environment = user_profile.environments.first()
                if not current_environment:
                    return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

            # Create tailored activity for the user
            tailored_activity = ActivityTailored.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                instructions=data.get('instructions', data.get('description', '')),
                created_on=datetime.now().date(),
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment,
                base_challenge_rating=data.get('base_challenge_rating', 50),
                challengingness={},  # Empty for now
                version=1,
                tailorization_level=100,  # Fully custom
                created_by=user_profile,  # Mark as user-created
                duration_range=data.get('duration_range', '15-30 minutes'),
                social_requirements={}
            )

            return JsonResponse({
                'success': True,
                'activity': {
                    'id': f'tailored-{tailored_activity.id}',
                    'name': tailored_activity.name,
                    'description': tailored_activity.description,
                    'domain': 'custom',
                    'base_challenge_rating': tailored_activity.base_challenge_rating,
                    'type': 'tailored',
                    'icon': '👤'
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TailorActivityView(View):
    """
    API endpoint to create a tailored version of a generic activity
    """

    def post(self, request):
        try:
            from apps.activity.models import GenericActivity, ActivityTailored
            from apps.user.models import UserProfile
            from datetime import datetime
            import json

            # Parse request data
            data = json.loads(request.body)
            generic_activity_id = data.get('generic_activity_id')

            if not generic_activity_id:
                return JsonResponse({'success': False, 'error': 'generic_activity_id required'}, status=400)

            # Get user profile
            user_profile = None
            if request.user.is_authenticated:
                try:
                    user_profile = UserProfile.objects.get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)
            else:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get generic activity
            try:
                # Handle both 'generic-123' and '123' formats
                if generic_activity_id.startswith('generic-'):
                    generic_id = generic_activity_id.replace('generic-', '')
                else:
                    generic_id = generic_activity_id

                generic_activity = GenericActivity.objects.get(id=generic_id)
            except GenericActivity.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Generic activity not found'}, status=404)

            # Get user's current environment
            current_environment = user_profile.current_environment
            if not current_environment:
                current_environment = user_profile.environments.first()
                if not current_environment:
                    return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

            # Check if tailored version already exists
            existing_tailored = ActivityTailored.objects.filter(
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment
            ).first()

            if existing_tailored:
                # Return existing tailored activity
                return JsonResponse({
                    'success': True,
                    'activity': {
                        'id': f'tailored-{existing_tailored.id}',
                        'name': existing_tailored.name,
                        'description': existing_tailored.description,
                        'domain': 'personalized',
                        'base_challenge_rating': existing_tailored.base_challenge_rating,
                        'type': 'tailored',
                        'icon': '⭐'
                    }
                })

            # Create new tailored activity
            tailored_activity = ActivityTailored.objects.create(
                name=generic_activity.name,
                description=generic_activity.description,
                instructions=generic_activity.instructions,
                created_on=datetime.now().date(),
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=current_environment,
                base_challenge_rating=50,  # Default challenge rating for tailored activities
                challengingness={},  # Would be populated by tailoring logic
                version=1,
                tailorization_level=50,  # Moderate tailoring
                created_by=None,  # System-generated
                duration_range=generic_activity.duration_range,
                social_requirements={}
            )

            return JsonResponse({
                'success': True,
                'activity': {
                    'id': f'tailored-{tailored_activity.id}',
                    'name': tailored_activity.name,
                    'description': tailored_activity.description,
                    'domain': 'personalized',
                    'base_challenge_rating': tailored_activity.base_challenge_rating,
                    'type': 'tailored',
                    'icon': '⭐'
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class UserFeedbackView(View):
    """
    API endpoint to create user feedback for various content types
    """

    def post(self, request):
        try:
            from apps.main.models import UserFeedback
            from apps.user.models import UserProfile
            from django.contrib.contenttypes.models import ContentType
            from django.contrib.auth.models import User
            import json

            # Parse request data
            data = json.loads(request.body)

            # Get user profile - handle debug mode and authentication
            user_profile = None
            user = None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            if is_debug_mode:
                # In debug mode, use a default test user (PhiPhi - ID: 3)
                try:
                    user = User.objects.get(id=3)  # PhiPhi user
                except User.DoesNotExist:
                    # Fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').all()
                    if user_profiles:
                        user = user_profiles.first().user
                    else:
                        return JsonResponse({'success': False, 'error': 'No user available for feedback'}, status=401)
            else:
                # Production mode - require proper authentication
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                elif request.user.is_authenticated:
                    user = request.user
                else:
                    return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile
            try:
                user_profile = UserProfile.objects.get(user=user)
            except UserProfile.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # Validate required fields
            feedback_type = data.get('feedback_type')
            content_type_name = data.get('content_type')
            object_id = data.get('object_id')
            user_comment = data.get('user_comment', '')

            if not all([feedback_type, content_type_name, object_id]):
                return JsonResponse({
                    'success': False,
                    'error': 'feedback_type, content_type, and object_id are required'
                }, status=400)

            # Get content type
            try:
                content_type = ContentType.objects.get(model=content_type_name.lower())
            except ContentType.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid content_type: {content_type_name}'
                }, status=400)

            # Create feedback
            feedback = UserFeedback.objects.create(
                feedback_type=feedback_type,
                content_type=content_type,
                object_id=str(object_id),
                user_profile=user_profile,
                user_comment=user_comment,
                criticality=data.get('criticality', 1),
                context_data=data.get('context_data', {}),
                slack_payload={}
            )

            return JsonResponse({
                'success': True,
                'feedback_id': feedback.id,
                'message': 'Feedback submitted successfully'
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class WheelItemManagementView(View):
    """
    API endpoint to manage wheel items (add/remove)
    """

    def delete(self, request, wheel_item_id):
        """Remove a wheel item and trigger wheel regeneration"""
        try:
            from apps.main.models import WheelItem, Wheel
            from apps.user.models import UserProfile
            from django.contrib.auth.models import User
            import json

            # Get user profile - handle debug mode and authentication
            user_profile = None
            user = None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            if is_debug_mode:
                # In debug mode, use a default test user (PhiPhi - ID: 3)
                try:
                    user = User.objects.get(id=3)  # PhiPhi user
                except User.DoesNotExist:
                    # Fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').all()
                    if user_profiles:
                        user = user_profiles.first().user
                    else:
                        return JsonResponse({'success': False, 'error': 'No user available'}, status=401)
            else:
                # Production mode - require proper authentication
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                elif request.user.is_authenticated:
                    user = request.user
                else:
                    return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile
            try:
                user_profile = UserProfile.objects.get(user=user)
            except UserProfile.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # ROBUST SOLUTION: Handle different wheel item ID formats with intelligent fallback
            try:
                # First, get the user's current wheel to ensure we're working with the right data
                wheels = Wheel.objects.filter(name__icontains=user_profile.profile_name).order_by('-created_at')
                if not wheels.exists():
                    return JsonResponse({'success': False, 'error': 'No wheel found for user'}, status=404)

                current_wheel = wheels.first()
                wheel_item = None

                # Try to find by exact database ID first (for database-created items)
                try:
                    wheel_item = WheelItem.objects.select_related('wheel').get(id=wheel_item_id, wheel=current_wheel)
                    logger.debug(f"✅ Found wheel item by exact database ID: {wheel_item_id}")
                except WheelItem.DoesNotExist:
                    logger.debug(f"⚠️ Wheel item not found by exact ID: {wheel_item_id}")

                    # ROBUST FALLBACK: Parse workflow-generated IDs like "wheel-item-1-abc123"
                    # Extract the item index and find by position
                    if wheel_item_id.startswith('wheel-item-'):
                        try:
                            # Extract index from "wheel-item-{index}-{hash}"
                            parts = wheel_item_id.split('-')
                            if len(parts) >= 3 and parts[2].isdigit():
                                item_index = int(parts[2]) - 1  # Convert to 0-based index

                                # Get all wheel items ordered by creation
                                all_items = list(WheelItem.objects.filter(wheel=current_wheel).order_by('id'))

                                if 0 <= item_index < len(all_items):
                                    wheel_item = all_items[item_index]
                                    logger.info(f"✅ ROBUST SOLUTION: Found wheel item by position {item_index + 1}: {wheel_item.id}")
                                else:
                                    logger.warning(f"⚠️ Item index {item_index + 1} out of range (1-{len(all_items)})")
                            else:
                                logger.warning(f"⚠️ Could not parse item index from ID: {wheel_item_id}")
                        except (ValueError, IndexError) as e:
                            logger.warning(f"⚠️ Error parsing wheel item ID {wheel_item_id}: {e}")

                if not wheel_item:
                    # Provide detailed error information for debugging
                    logger.error(f"Wheel item {wheel_item_id} not found in user's current wheel")

                    # Check if the ID looks like an activity ID instead of wheel item ID
                    if wheel_item_id.startswith('llm_tailored_') or wheel_item_id.startswith('generic-'):
                        return JsonResponse({
                            'success': False,
                            'error': f'Invalid wheel item ID format. Received activity ID "{wheel_item_id}" instead of wheel item ID. This indicates a frontend data synchronization issue.'
                        }, status=400)
                    else:
                        return JsonResponse({'success': False, 'error': f'Wheel item "{wheel_item_id}" not found'}, status=404)

            except Exception as e:
                logger.error(f"Error finding wheel item {wheel_item_id}: {str(e)}", exc_info=True)
                return JsonResponse({'success': False, 'error': f'Error finding wheel item: {str(e)}'}, status=500)

            # Store wheel reference and item info before deletion
            wheel = wheel_item.wheel
            removed_item_name = wheel_item.activity_tailored.name
            removed_item_db_id = wheel_item.id

            # Remove the wheel item
            wheel_item.delete()
            logger.info(f"✅ ROBUST SOLUTION: Removed wheel item '{removed_item_name}' (DB ID: {removed_item_db_id}) for request ID: {wheel_item_id}")

            # Recalculate percentages for remaining items
            remaining_items = WheelItem.objects.filter(wheel=wheel)
            if remaining_items.exists():
                # Redistribute percentages equally among remaining items
                new_percentage = 100.0 / remaining_items.count()
                for item in remaining_items:
                    item.percentage = new_percentage
                    item.save()

            # Return updated wheel data
            updated_items = []
            for item in remaining_items:
                updated_items.append({
                    'id': item.id,
                    'name': item.activity_tailored.name,
                    'description': item.activity_tailored.description,
                    'percentage': item.percentage,
                    'color': self._get_activity_color(item.activity_tailored),
                    'activity_tailored_id': item.activity_tailored.id
                })

            return JsonResponse({
                'success': True,
                'message': f'Wheel item "{removed_item_name}" removed successfully',
                'wheel_data': {
                    'segments': updated_items
                },
                'debug_info': {
                    'requested_id': wheel_item_id,
                    'database_id': removed_item_db_id,
                    'removed_item': removed_item_name
                }
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    def post(self, request):
        """Add an activity to the current wheel"""
        try:
            from apps.main.models import WheelItem, Wheel
            from apps.activity.models import ActivityTailored, GenericActivity
            from apps.user.models import UserProfile
            from django.contrib.auth.models import User
            import json

            # Parse request data
            data = json.loads(request.body)
            activity_id = data.get('activity_id')
            activity_type = data.get('activity_type', 'tailored')  # 'tailored' or 'generic'

            if not activity_id:
                return JsonResponse({'success': False, 'error': 'activity_id is required'}, status=400)

            # Get user profile - handle debug mode and authentication
            user_profile = None
            user = None

            # Check if we're in debug mode (development environment)
            is_debug_mode = getattr(settings, 'DEBUG', False)

            if is_debug_mode:
                # In debug mode, use a default test user (PhiPhi - ID: 3)
                try:
                    user = User.objects.get(id=3)  # PhiPhi user
                except User.DoesNotExist:
                    # Fallback to first available user with profile
                    user_profiles = UserProfile.objects.select_related('user').all()
                    if user_profiles:
                        user = user_profiles.first().user
                    else:
                        return JsonResponse({'success': False, 'error': 'No user available'}, status=401)
            else:
                # Production mode - require proper authentication
                auth_header = request.headers.get('Authorization', '')
                if auth_header.startswith('Bearer '):
                    # TODO: Implement proper token validation in production
                    return JsonResponse({'success': False, 'error': 'Token authentication not implemented'}, status=401)
                elif request.user.is_authenticated:
                    user = request.user
                else:
                    return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # Get user profile
            try:
                user_profile = UserProfile.objects.get(user=user)
            except UserProfile.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'User profile not found'}, status=400)

            # CRITICAL FIX: Always use the most recent wheel for the user
            # This prevents creating multiple wheels and maintains consistency
            wheels = Wheel.objects.filter(name__icontains=user_profile.profile_name).order_by('-created_at')
            if wheels.exists():
                wheel = wheels.first()
                logger.debug(f"Using existing wheel: {wheel.name} (ID: {wheel.id})")
            else:
                # Create new wheel only if none exists
                wheel = Wheel.objects.create(
                    name=f"{user_profile.profile_name}'s Activity Wheel",
                    created_by='user_manual_addition',
                    created_at=timezone.now().date()
                )
                logger.debug(f"Created new wheel: {wheel.name} (ID: {wheel.id})")

            # Get activity based on type
            activity_tailored = None
            if activity_type == 'tailored':
                # Handle tailored activity
                try:
                    # Remove 'tailored-' prefix if present
                    if activity_id.startswith('tailored-'):
                        activity_id = activity_id.replace('tailored-', '')
                    activity_tailored = ActivityTailored.objects.get(id=activity_id)
                except ActivityTailored.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'Tailored activity not found'}, status=404)
            else:
                # Handle generic activity - need to create tailored version
                try:
                    # Remove 'generic-' prefix if present
                    if activity_id.startswith('generic-'):
                        activity_id = activity_id.replace('generic-', '')
                    generic_activity = GenericActivity.objects.get(id=activity_id)

                    # Get user's current environment
                    current_environment = user_profile.current_environment
                    if not current_environment:
                        current_environment = user_profile.environments.first()
                        if not current_environment:
                            return JsonResponse({'success': False, 'error': 'No user environment found'}, status=400)

                    # Create or get existing tailored activity
                    activity_tailored, created = ActivityTailored.objects.get_or_create(
                        user_profile=user_profile,
                        generic_activity=generic_activity,
                        user_environment=current_environment,
                        defaults={
                            'name': generic_activity.name,
                            'description': generic_activity.description,
                            'instructions': generic_activity.instructions,
                            'created_on': timezone.now().date(),
                            'base_challenge_rating': 50,  # Default challenge rating
                            'challengingness': {},
                            'version': 1,
                            'tailorization_level': 50,
                            'created_by': None,
                            'duration_range': generic_activity.duration_range,
                            'social_requirements': {}
                        }
                    )
                except GenericActivity.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'Generic activity not found'}, status=404)

            # Check if activity is already in wheel
            existing_item = WheelItem.objects.filter(
                wheel=wheel,
                activity_tailored=activity_tailored
            ).first()

            if existing_item:
                return JsonResponse({
                    'success': False,
                    'error': 'Activity is already in the wheel'
                }, status=400)

            # Add new wheel item
            existing_items = WheelItem.objects.filter(wheel=wheel)
            item_count = existing_items.count() + 1  # Including the new item
            new_percentage = 100.0 / item_count

            # Create new wheel item
            new_wheel_item = WheelItem.objects.create(
                id=f"item_{int(time.time())}_{activity_tailored.id}",
                wheel=wheel,
                percentage=new_percentage,
                activity_tailored=activity_tailored
            )

            # Redistribute percentages for all items
            all_items = WheelItem.objects.filter(wheel=wheel)
            for item in all_items:
                item.percentage = new_percentage
                item.save()

            # Return updated wheel data
            updated_items = []
            for item in all_items:
                updated_items.append({
                    'id': item.id,
                    'name': item.activity_tailored.name,
                    'description': item.activity_tailored.description,
                    'percentage': item.percentage,
                    'color': self._get_activity_color(item.activity_tailored),
                    'activity_tailored_id': item.activity_tailored.id
                })

            return JsonResponse({
                'success': True,
                'message': 'Activity added to wheel successfully',
                'wheel_data': {
                    'segments': updated_items
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    def _get_activity_color(self, activity_tailored):
        """Get color for activity based on domain or default"""
        # Simple color mapping - could be enhanced with domain-based colors
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
        ]
        # Use activity ID to get consistent color
        color_index = hash(str(activity_tailored.id)) % len(colors)
        return colors[color_index]


@method_decorator(csrf_exempt, name='dispatch')
class UserProfileDetailView(View):
    """
    API endpoint to get comprehensive user profile data
    """

    def get(self, request, user_id=None):
        try:
            from apps.user.models import UserProfile, Demographics, Preference, UserEnvironment

            # SECURITY FIX: Ensure user authentication
            if not request.user.is_authenticated:
                return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)

            # SECURITY FIX: Authorization check - users can only access their own profile
            # unless they are staff (for debug purposes)
            if user_id:
                if not request.user.is_staff:
                    return JsonResponse({'success': False, 'error': 'Access denied'}, status=403)
                try:
                    user_profile = UserProfile.objects.select_related('user', 'current_environment').get(id=user_id)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=404)
            else:
                # Get authenticated user's own profile
                try:
                    user_profile = UserProfile.objects.select_related('user', 'current_environment').get(user=request.user)
                except UserProfile.DoesNotExist:
                    return JsonResponse({'success': False, 'error': 'User profile not found'}, status=404)

            # Get demographics
            demographics = None
            try:
                demo = Demographics.objects.get(user_profile=user_profile)
                demographics = {
                    'full_name': demo.full_name,
                    'age': demo.age,
                    'gender': demo.gender,
                    'location': demo.location,
                    'language': demo.language,
                    'occupation': demo.occupation,
                }
            except Demographics.DoesNotExist:
                pass

            # Get current environment details
            environment = None
            if user_profile.current_environment:
                env = user_profile.current_environment
                environment = {
                    'id': env.id,
                    'name': env.environment_name,
                    'description': env.environment_description,
                    'living_situation': env.environment_details.get('living_situation', 'Not specified'),
                    'available_resources': env.environment_details.get('available_resources', 'Not specified'),
                    'constraints': env.environment_details.get('constraints', 'Not specified'),
                    'is_current': env.is_current
                }

            # Get user environments
            environments = []
            for env in UserEnvironment.objects.filter(user_profile=user_profile):
                environments.append({
                    'id': env.id,
                    'name': env.environment_name,
                    'description': env.environment_description,
                    'is_current': env.is_current,
                    'details': env.environment_details
                })

            # Get preferences
            preferences = []
            for pref in Preference.objects.filter(user_profile=user_profile):
                preferences.append({
                    'name': pref.pref_name,
                    'description': pref.pref_description,
                    'strength': pref.pref_strength,
                })

            # Build comprehensive profile data
            profile_data = {
                'id': str(user_profile.id),
                'name': user_profile.profile_name,
                'description': f"User profile for {user_profile.profile_name}",
                'is_real': user_profile.is_real,
                'demographics': demographics,
                'environment': environment,
                'environments': environments,
                'preferences': preferences,
                'goals': {
                    'short_term': 'Not set',  # These would come from a Goals model if implemented
                    'long_term': 'Not set',
                    'motivation': 'Not set'
                },
                'created_at': getattr(user_profile, 'created_at', None)
            }

            return JsonResponse({
                'success': True,
                'profile': profile_data
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class BetaSignupView(View):
    """
    API endpoint to handle beta signup requests from interested users
    """

    def post(self, request):
        try:
            from apps.main.models import BetaSignup
            import json
            import re

            # Parse request data
            data = json.loads(request.body)
            email = data.get('email', '').strip().lower()
            message = data.get('message', '').strip()

            # Validate email
            if not email:
                return JsonResponse({
                    'success': False,
                    'error': 'Email address is required'
                }, status=400)

            # Basic email validation
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return JsonResponse({
                    'success': False,
                    'error': 'Please enter a valid email address'
                }, status=400)

            # Check if email already exists
            if BetaSignup.objects.filter(email=email).exists():
                return JsonResponse({
                    'success': False,
                    'error': 'This email address has already been registered for beta access'
                }, status=400)

            # Get client metadata
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # Create beta signup record
            beta_signup = BetaSignup.objects.create(
                email=email,
                message=message,
                ip_address=ip_address,
                user_agent=user_agent
            )

            return JsonResponse({
                'success': True,
                'message': 'Thank you for your interest! We\'ll contact you when beta access becomes available.',
                'signup_id': str(beta_signup.id)
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid request data'
            }, status=400)
        except Exception as e:
            logger.error(f"Beta signup error: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': 'An error occurred while processing your request. Please try again.'
            }, status=500)

    def _get_client_ip(self, request):
        """Get the client's IP address from the request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
