# config/settings/prod.py
# Production settings for all-in-one Digital Ocean deployment

import os
from pathlib import Path
from django.core.management.utils import get_random_secret_key
import dj_database_url
from .base import *

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Security settings
DEBUG = os.getenv("DEBUG", "False") == "True"
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Hosts configuration - App Platform provides APP_DOMAIN
ALLOWED_HOSTS = os.getenv("DJANGO_ALLOWED_HOSTS", "127.0.0.1,localhost").split(",")

# Secret key from environment with fallback generation
SECRET_KEY = os.getenv("DJANGO_SECRET_KEY", get_random_secret_key())

# Development mode flag for local testing
DEVELOPMENT_MODE = os.getenv("DEVELOPMENT_MODE", "False") == "True"

# Database configuration for App Platform
if DEVELOPMENT_MODE is True:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": os.path.join(BASE_DIR, "db.sqlite3"),
        }
    }
else:
    if os.getenv("DATABASE_URL", None) is None:
        raise Exception("DATABASE_URL environment variable not defined")
    DATABASES = {
        "default": dj_database_url.parse(os.environ.get("DATABASE_URL")),
    }

# Cache configuration - Local Redis
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'KEY_PREFIX': 'goali',
        'TIMEOUT': 300,
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_SECURE = False  # Digital Ocean handles HTTPS termination
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_AGE = 3600

# CSRF security
CSRF_COOKIE_SECURE = False  # Digital Ocean handles HTTPS termination
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = [
    'https://*.ondigitalocean.app',  # Digital Ocean app domain
    'https://goali-secure-aec2e.ondigitalocean.app',  # Your specific app
]

# Celery configuration - Local Redis
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://127.0.0.1:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_BROKER_URL', 'redis://127.0.0.1:6379/0')
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 300
CELERY_TASK_SOFT_TIME_LIMIT = 240
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 100

# Channels configuration - Use in-memory for App Platform (single instance)
# For production with multiple instances, you would need a managed Redis
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# If Redis is available, use it for Channels
REDIS_URL = os.environ.get('REDIS_URL')
if REDIS_URL:
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                'hosts': [REDIS_URL],
                'capacity': 1500,
                'expiry': 10,
            },
        },
    }

# CORS configuration - Allow App Platform domains
CORS_ALLOWED_ORIGINS = []
if os.getenv("APP_DOMAIN"):
    CORS_ALLOWED_ORIGINS.extend([
        f"https://{os.getenv('APP_DOMAIN')}",
        f"http://{os.getenv('APP_DOMAIN')}",  # For development
    ])

# Allow all origins in debug mode, otherwise be restrictive
CORS_ALLOW_ALL_ORIGINS = DEBUG
CORS_ALLOW_CREDENTIALS = True

# Static files configuration for App Platform
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

# Static files directories - only Django app static files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Use WhiteNoise for static file serving in production
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'goali': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Application execution mode
GOALI_DEFAULT_EXECUTION_MODE = os.environ.get('GOALI_DEFAULT_EXECUTION_MODE', 'production')

# LLM configuration - Using environment variables (securely managed by Digital Ocean)
MISTRAL_API_KEY = os.environ.get('MISTRAL_API_KEY')
DEFAULT_LLM_MODEL_NAME = os.environ.get('DEFAULT_LLM_MODEL_NAME', 'mistral-small-latest')
DEFAULT_LLM_TEMPERATURE = float(os.environ.get('DEFAULT_LLM_TEMPERATURE', '0.7'))

# Performance optimizations for single container
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# Security middleware with WhiteNoise for static files
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Add WhiteNoise for static files
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# API throttling for single instance
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '50/hour',
        'user': '500/hour'
    }
}

# Time zone
USE_TZ = True
TIME_ZONE = 'UTC'

# Internationalization
LANGUAGE_CODE = 'en-us'
USE_I18N = True
USE_L10N = True

# Default auto field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# File upload settings
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755

# WebSocket settings
WEBSOCKET_ACCEPT_ALL = False

# Email configuration (basic setup for now)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# Monitoring
ENABLE_PERFORMANCE_MONITORING = True
ENABLE_ERROR_TRACKING = True

# Validate required environment variables
required_env_vars = ['DJANGO_SECRET_KEY', 'MISTRAL_API_KEY']
missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

if missing_vars:
    print(f"⚠️  WARNING: Missing environment variables: {missing_vars}")
    if DEBUG:
        print("🔧 Running in debug mode - using fallback values")
    else:
        print("❌ Required environment variables missing in production!")
        # Don't exit in production deployment, let Digital Ocean handle this

# Success message
print(f"✅ Production settings loaded successfully")
print(f"   Environment: {GOALI_DEFAULT_EXECUTION_MODE}")
print(f"   Database: {'Local PostgreSQL' if 'localhost' in str(DATABASES['default'].get('HOST', '')) else 'External'}")
print(f"   Cache: {'Local Redis' if '127.0.0.1' in CACHES['default']['LOCATION'] else 'External'}")
print(f"   Secrets: {'✅ Loaded' if os.environ.get('MISTRAL_API_KEY') else '❌ Missing'}")
print(f"   CORS Origins: {CORS_ALLOWED_ORIGINS}")
