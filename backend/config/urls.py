"""config URL Configuration"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.http import JsonResponse
from django.utils import timezone

# Import the custom admin site INSTANCE
from config.admin import admin_site # Import custom admin instance

def health_check_root(request):
    """Root level health check for Digital Ocean"""
    return JsonResponse({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'service': 'goali-backend',
        'version': '1.0.0'
    })

urlpatterns = [
    # Admin URLs - must come first to avoid conflicts
    path('admin/', admin_site.urls),  # Use the imported custom admin site instance (includes admin_tools URLs now)

    # Main app URLs (includes health check and API endpoints)
    path('', include('apps.main.urls')),

    # Coverage dashboard
    path('coverage/', include('apps.coverage_dashboard.urls')),

    # Fallback health check for root level (Digital Ocean compatibility)
    path('health/', health_check_root, name='health_check_root'),
    path('health', health_check_root, name='health_check_root_no_slash'),
]

# Explicitly add staticfiles patterns for development
urlpatterns += staticfiles_urlpatterns()

# Serve static files in development
if settings.DEBUG:
    # Add static files from STATIC_ROOT (for admin)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Also serve from STATICFILES_DIRS if specified
    for staticfiles_dir in settings.STATICFILES_DIRS:
        urlpatterns += static(settings.STATIC_URL, document_root=staticfiles_dir)
    
    # Media files (if applicable)
    if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_ROOT:
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
