"""config URL Configuration"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.http import JsonResponse
from django.utils import timezone

# Import the custom admin site INSTANCE
from config.admin import admin_site # Import custom admin instance

def health_check_root(request):
    """Root level health check for Digital Ocean"""
    return JsonResponse({
        'status': 'healthy',
        'timestamp': timezone.now().isoformat(),
        'service': 'goali-backend',
        'version': '1.0.0'
    })

def admin_path_handler(request, path=''):
    """
    Handle admin paths that have been stripped of /admin prefix by DigitalOcean ingress.
    When DO strips /admin prefix, /admin/user/userprofile/ becomes /user/userprofile/
    We need to restore the admin context and serve the admin interface.
    """
    from django.http import HttpResponse
    from django.urls import resolve, reverse
    from django.conf import settings
    import re

    # Reconstruct the original admin path
    admin_path = f'/admin/{path}'

    # Update request path info to match admin expectations
    request.path_info = admin_path
    request.path = admin_path

    # Use the admin site's URL resolver
    try:
        # Get the admin URLconf and resolve the path
        from django.urls import get_resolver
        resolver = get_resolver()

        # Try to resolve the admin path
        match = resolver.resolve(admin_path)

        # Call the matched view with the modified request
        return match.func(request, *match.args, **match.kwargs)

    except Exception as e:
        # If resolution fails, fall back to admin index
        return admin_site.index(request)

urlpatterns = [
    # Admin URLs - handle both normal and stripped paths
    path('admin/', admin_site.urls),

    # Handle stripped admin paths from DigitalOcean ingress
    # These patterns catch admin sub-paths after /admin prefix is stripped
    path('user/', admin_path_handler, {'path': 'user/'}),
    path('user/<path:subpath>', admin_path_handler),
    path('auth/', admin_path_handler, {'path': 'auth/'}),
    path('auth/<path:subpath>', admin_path_handler),
    path('main/', admin_path_handler, {'path': 'main/'}),
    path('main/<path:subpath>', admin_path_handler),
    path('coverage_dashboard/', admin_path_handler, {'path': 'coverage_dashboard/'}),
    path('coverage_dashboard/<path:subpath>', admin_path_handler),

    # Main app URLs (includes health check and API endpoints)
    path('', include('apps.main.urls')),

    # Coverage dashboard
    path('coverage/', include('apps.coverage_dashboard.urls')),

    # Fallback health check for root level (Digital Ocean compatibility)
    path('health/', health_check_root, name='health_check_root'),
    path('health', health_check_root, name='health_check_root_no_slash'),
]

# Explicitly add staticfiles patterns for development
urlpatterns += staticfiles_urlpatterns()

# Serve static files in development
if settings.DEBUG:
    # Add static files from STATIC_ROOT (for admin)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Also serve from STATICFILES_DIRS if specified
    for staticfiles_dir in settings.STATICFILES_DIRS:
        urlpatterns += static(settings.STATIC_URL, document_root=staticfiles_dir)
    
    # Media files (if applicable)
    if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_ROOT:
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
